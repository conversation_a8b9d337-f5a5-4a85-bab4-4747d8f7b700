package com.Hamode.periodpal.data.models

import java.time.LocalDate

/**
 * Represents health insights and analytics
 */
data class HealthInsight(
    val id: String = "",
    val type: InsightType,
    val title: String,
    val description: String,
    val recommendation: String = "",
    val severity: InsightSeverity = InsightSeverity.INFO,
    val dateGenerated: LocalDate = LocalDate.now(),
    val isRead: Boolean = false,
    val relatedData: Map<String, Any> = emptyMap()
)

/**
 * Types of health insights
 */
enum class InsightType(val displayName: String) {
    CYCLE_PATTERN("Cycle Pattern"),
    SYMPTOM_TREND("Symptom Trend"),
    MOOD_PATTERN("Mood Pattern"),
    PAIN_ANALYSIS("Pain Analysis"),
    FERTILITY_INSIGHT("Fertility Insight"),
    HEALTH_RECOMMENDATION("Health Recommendation"),
    CYCLE_IRREGULARITY("Cycle Irregularity"),
    SYMPTOM_CORRELATION("Symptom Correlation")
}

/**
 * Severity levels for insights
 */
enum class InsightSeverity(val displayName: String, val color: String) {
    INFO("Information", "#2196F3"),
    LOW("Low Priority", "#4CAF50"),
    MEDIUM("Medium Priority", "#FF9800"),
    HIGH("High Priority", "#F44336"),
    URGENT("Urgent", "#9C27B0")
}

/**
 * Cycle statistics and analytics
 */
data class CycleStatistics(
    val averageCycleLength: Double,
    val averagePeriodLength: Double,
    val cycleVariability: Double, // standard deviation
    val totalCycles: Int,
    val longestCycle: Int,
    val shortestCycle: Int,
    val mostCommonSymptoms: List<Pair<SymptomType, Int>>, // symptom to frequency
    val averagePainLevel: Double,
    val moodTrends: Map<MoodType, Int>, // mood to frequency
    val fertileWindowAccuracy: Double? = null, // if tracking ovulation
    val lastUpdated: LocalDate = LocalDate.now()
) {
    /**
     * Determines if cycles are regular (variability < 7 days)
     */
    fun areRegular(): Boolean = cycleVariability < 7.0
    
    /**
     * Gets cycle regularity description
     */
    fun getRegularityDescription(): String {
        return when {
            cycleVariability < 3.0 -> "Very Regular"
            cycleVariability < 7.0 -> "Regular"
            cycleVariability < 14.0 -> "Somewhat Irregular"
            else -> "Irregular"
        }
    }
    
    /**
     * Gets the most problematic symptoms (high frequency + severity)
     */
    fun getProblematicSymptoms(): List<SymptomType> {
        return mostCommonSymptoms
            .filter { it.second >= totalCycles / 2 } // appears in at least half of cycles
            .map { it.first }
            .take(3)
    }
}

/**
 * Prediction data for future cycles
 */
data class CyclePrediction(
    val nextPeriodStart: LocalDate,
    val nextPeriodEnd: LocalDate,
    val nextFertileWindowStart: LocalDate,
    val nextFertileWindowEnd: LocalDate,
    val nextPMSStart: LocalDate,
    val confidence: Double, // 0.0 to 1.0
    val basedOnCycles: Int, // number of cycles used for prediction
    val generatedDate: LocalDate = LocalDate.now()
) {
    /**
     * Gets confidence level description
     */
    fun getConfidenceDescription(): String {
        return when {
            confidence >= 0.9 -> "Very High"
            confidence >= 0.7 -> "High"
            confidence >= 0.5 -> "Medium"
            confidence >= 0.3 -> "Low"
            else -> "Very Low"
        }
    }
}

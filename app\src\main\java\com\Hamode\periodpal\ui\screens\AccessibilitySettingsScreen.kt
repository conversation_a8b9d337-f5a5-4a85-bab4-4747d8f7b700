package com.Hamode.periodpal.ui.screens

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.Hamode.periodpal.ui.accessibility.*
import com.Hamode.periodpal.ui.components.AccessibleButton
import com.Hamode.periodpal.ui.theme.*

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AccessibilitySettingsScreen(
    onBack: () -> Unit,
    modifier: Modifier = Modifier
) {
    var accessibilitySettings by remember { mutableStateOf(AccessibilitySettings()) }
    
    Column(
        modifier = modifier
            .fillMaxSize()
            .background(
                brush = Brush.verticalGradient(
                    colors = listOf(
                        MaterialTheme.colorScheme.background,
                        SoftPink80.copy(alpha = 0.1f)
                    )
                )
            )
    ) {
        // Top App Bar
        TopAppBar(
            title = {
                Text(
                    text = "Accessibility Settings",
                    style = AccessibilityUtils.getAccessibleTextStyle(
                        MaterialTheme.typography.titleLarge,
                        accessibilitySettings
                    ),
                    fontWeight = FontWeight.SemiBold
                )
            },
            navigationIcon = {
                IconButton(onClick = onBack) {
                    Icon(
                        imageVector = Icons.Default.ArrowBack,
                        contentDescription = "Back to settings"
                    )
                }
            },
            colors = TopAppBarDefaults.topAppBarColors(
                containerColor = MaterialTheme.colorScheme.surface
            )
        )
        
        // Content
        Column(
            modifier = Modifier
                .fillMaxSize()
                .verticalScroll(rememberScrollState())
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // Visual Accessibility Section
            AccessibilitySection(
                title = "Visual Accessibility",
                icon = Icons.Default.Visibility,
                settings = accessibilitySettings
            ) {
                // High Contrast Mode
                AccessibilityToggleItem(
                    title = "High Contrast Mode",
                    description = "Increases color contrast for better visibility",
                    isEnabled = accessibilitySettings.isHighContrastEnabled,
                    onToggle = { 
                        accessibilitySettings = accessibilitySettings.copy(isHighContrastEnabled = it)
                    },
                    settings = accessibilitySettings
                )
                
                // Large Text
                AccessibilityToggleItem(
                    title = "Large Text",
                    description = "Increases text size throughout the app",
                    isEnabled = accessibilitySettings.isLargeTextEnabled,
                    onToggle = { 
                        accessibilitySettings = accessibilitySettings.copy(isLargeTextEnabled = it)
                    },
                    settings = accessibilitySettings
                )
                
                // Color Blind Friendly
                AccessibilityToggleItem(
                    title = "Color Blind Friendly",
                    description = "Uses patterns and shapes in addition to colors",
                    isEnabled = accessibilitySettings.isColorBlindFriendlyEnabled,
                    onToggle = { 
                        accessibilitySettings = accessibilitySettings.copy(isColorBlindFriendlyEnabled = it)
                    },
                    settings = accessibilitySettings
                )
                
                // Text Scale Slider
                TextScaleSlider(
                    textScaleFactor = accessibilitySettings.textScaleFactor,
                    onScaleChanged = { 
                        accessibilitySettings = accessibilitySettings.copy(textScaleFactor = it)
                    },
                    settings = accessibilitySettings
                )
            }
            
            // Motion Accessibility Section
            AccessibilitySection(
                title = "Motion & Animation",
                icon = Icons.Default.Animation,
                settings = accessibilitySettings
            ) {
                AccessibilityToggleItem(
                    title = "Reduce Motion",
                    description = "Minimizes animations and transitions",
                    isEnabled = accessibilitySettings.isReduceMotionEnabled,
                    onToggle = { 
                        accessibilitySettings = accessibilitySettings.copy(isReduceMotionEnabled = it)
                    },
                    settings = accessibilitySettings
                )
            }
            
            // Navigation Accessibility Section
            AccessibilitySection(
                title = "Navigation & Focus",
                icon = Icons.Default.TouchApp,
                settings = accessibilitySettings
            ) {
                AccessibilityToggleItem(
                    title = "Focus Indicators",
                    description = "Shows visual focus indicators for keyboard navigation",
                    isEnabled = accessibilitySettings.isFocusIndicatorEnabled,
                    onToggle = { 
                        accessibilitySettings = accessibilitySettings.copy(isFocusIndicatorEnabled = it)
                    },
                    settings = accessibilitySettings
                )
                
                // Touch Target Size Info
                TouchTargetSizeInfo(
                    minimumSize = accessibilitySettings.minimumTouchTargetSize,
                    settings = accessibilitySettings
                )
            }
            
            // Screen Reader Section
            AccessibilitySection(
                title = "Screen Reader Support",
                icon = Icons.Default.RecordVoiceOver,
                settings = accessibilitySettings
            ) {
                ScreenReaderInfo(settings = accessibilitySettings)
            }
            
            // Test Accessibility
            AccessibilityTestSection(
                settings = accessibilitySettings
            )
            
            // Bottom spacing
            Spacer(modifier = Modifier.height(80.dp))
        }
    }
}

@Composable
private fun AccessibilitySection(
    title: String,
    icon: ImageVector,
    settings: AccessibilitySettings,
    content: @Composable ColumnScope.() -> Unit
) {
    Card(
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.padding(bottom = 16.dp)
            ) {
                Icon(
                    imageVector = icon,
                    contentDescription = null,
                    tint = AccessibilityUtils.getAccessibleColor(RosePink40, settings = settings),
                    modifier = Modifier.size(24.dp)
                )
                
                Spacer(modifier = Modifier.width(12.dp))
                
                Text(
                    text = title,
                    style = AccessibilityUtils.getAccessibleTextStyle(
                        MaterialTheme.typography.titleMedium,
                        settings
                    ),
                    color = MaterialTheme.colorScheme.onSurface,
                    fontWeight = FontWeight.SemiBold
                )
            }
            
            content()
        }
    }
}

@Composable
private fun AccessibilityToggleItem(
    title: String,
    description: String,
    isEnabled: Boolean,
    onToggle: (Boolean) -> Unit,
    settings: AccessibilitySettings
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Column(modifier = Modifier.weight(1f)) {
            Text(
                text = title,
                style = AccessibilityUtils.getAccessibleTextStyle(
                    MaterialTheme.typography.titleSmall,
                    settings
                ),
                color = MaterialTheme.colorScheme.onSurface,
                fontWeight = FontWeight.Medium
            )
            
            Text(
                text = description,
                style = AccessibilityUtils.getAccessibleTextStyle(
                    MaterialTheme.typography.bodySmall,
                    settings
                ),
                color = DarkGray.copy(alpha = 0.7f)
            )
        }
        
        Switch(
            checked = isEnabled,
            onCheckedChange = onToggle,
            colors = SwitchDefaults.colors(
                checkedThumbColor = AccessibilityUtils.getAccessibleColor(RosePink40, settings = settings),
                checkedTrackColor = AccessibilityUtils.getAccessibleColor(RosePink40.copy(alpha = 0.3f), settings = settings)
            )
        )
    }
}

@Composable
private fun TextScaleSlider(
    textScaleFactor: Float,
    onScaleChanged: (Float) -> Unit,
    settings: AccessibilitySettings
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp)
    ) {
        Text(
            text = "Text Size: ${(textScaleFactor * 100).toInt()}%",
            style = AccessibilityUtils.getAccessibleTextStyle(
                MaterialTheme.typography.titleSmall,
                settings
            ),
            color = MaterialTheme.colorScheme.onSurface,
            fontWeight = FontWeight.Medium
        )
        
        Slider(
            value = textScaleFactor,
            onValueChange = onScaleChanged,
            valueRange = 0.8f..2.0f,
            steps = 11,
            colors = SliderDefaults.colors(
                thumbColor = AccessibilityUtils.getAccessibleColor(RosePink40, settings = settings),
                activeTrackColor = AccessibilityUtils.getAccessibleColor(RosePink40, settings = settings)
            )
        )
        
        Text(
            text = "Preview text at current size",
            style = AccessibilityUtils.getAccessibleTextStyle(
                MaterialTheme.typography.bodyMedium,
                settings
            ),
            color = DarkGray.copy(alpha = 0.7f)
        )
    }
}

@Composable
private fun TouchTargetSizeInfo(
    minimumSize: androidx.compose.ui.unit.Dp,
    settings: AccessibilitySettings
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp)
    ) {
        Text(
            text = "Touch Target Size",
            style = AccessibilityUtils.getAccessibleTextStyle(
                MaterialTheme.typography.titleSmall,
                settings
            ),
            color = MaterialTheme.colorScheme.onSurface,
            fontWeight = FontWeight.Medium
        )
        
        Text(
            text = "Minimum touch target size: $minimumSize (WCAG recommended: 44dp)",
            style = AccessibilityUtils.getAccessibleTextStyle(
                MaterialTheme.typography.bodySmall,
                settings
            ),
            color = DarkGray.copy(alpha = 0.7f)
        )
    }
}

@Composable
private fun ScreenReaderInfo(
    settings: AccessibilitySettings
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp)
    ) {
        Text(
            text = "Screen Reader Compatibility",
            style = AccessibilityUtils.getAccessibleTextStyle(
                MaterialTheme.typography.titleSmall,
                settings
            ),
            color = MaterialTheme.colorScheme.onSurface,
            fontWeight = FontWeight.Medium
        )
        
        Text(
            text = "PeriodPal is optimized for TalkBack and other screen readers with detailed content descriptions and semantic markup.",
            style = AccessibilityUtils.getAccessibleTextStyle(
                MaterialTheme.typography.bodySmall,
                settings
            ),
            color = DarkGray.copy(alpha = 0.7f)
        )
    }
}

@Composable
private fun AccessibilityTestSection(
    settings: AccessibilitySettings
) {
    Card(
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = AccessibilityUtils.getAccessibleColor(
                InfoBlue.copy(alpha = 0.1f),
                settings = settings
            )
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "Test Your Settings",
                style = AccessibilityUtils.getAccessibleTextStyle(
                    MaterialTheme.typography.titleMedium,
                    settings
                ),
                color = MaterialTheme.colorScheme.onSurface,
                fontWeight = FontWeight.SemiBold,
                modifier = Modifier.padding(bottom = 12.dp)
            )
            
            Text(
                text = "Use the buttons below to test how your accessibility settings affect the app interface.",
                style = AccessibilityUtils.getAccessibleTextStyle(
                    MaterialTheme.typography.bodyMedium,
                    settings
                ),
                color = DarkGray.copy(alpha = 0.7f),
                modifier = Modifier.padding(bottom = 16.dp)
            )
            
            Row(
                horizontalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                AccessibleButton(
                    text = "Test Button",
                    onClick = { },
                    contentDescription = "Test button to verify accessibility settings"
                )
                
                AccessibleButton(
                    text = "Reset",
                    onClick = { },
                    contentDescription = "Reset accessibility settings to defaults",
                    colors = ButtonDefaults.outlinedButtonColors()
                )
            }
        }
    }
}

@Preview(showBackground = true, name = "Accessibility Settings Screen Preview")
@Composable
fun AccessibilitySettingsScreenPreview() {
    PeriodPalTheme {
        AccessibilitySettingsScreen(
            onBack = { }
        )
    }
}

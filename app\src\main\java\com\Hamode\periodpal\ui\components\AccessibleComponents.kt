package com.Hamode.periodpal.ui.components

import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.focusable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.selection.selectable
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.ripple.rememberRipple
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.scale
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.semantics.*
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.Hamode.periodpal.data.models.*
import com.Hamode.periodpal.ui.accessibility.*
import com.Hamode.periodpal.ui.theme.*

/**
 * Accessible button with proper touch targets and semantic information
 */
@Composable
fun AccessibleButton(
    text: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
    icon: ImageVector? = null,
    contentDescription: String? = null,
    colors: ButtonColors = ButtonDefaults.buttonColors(containerColor = RosePink40)
) {
    val accessibilitySettings = rememberAccessibilitySettings()
    val minTouchTarget = AccessibilityUtils.getMinimumTouchTargetSize(accessibilitySettings)
    
    Button(
        onClick = onClick,
        modifier = modifier
            .defaultMinSize(minWidth = minTouchTarget, minHeight = minTouchTarget)
            .semantics {
                contentDescription?.let { this.contentDescription = it }
                    ?: run { this.contentDescription = SemanticDescriptions.getButtonActionDescription(text) }
                role = Role.Button
                if (!enabled) {
                    disabled()
                }
            },
        enabled = enabled,
        colors = colors,
        shape = RoundedCornerShape(12.dp)
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            icon?.let {
                Icon(
                    imageVector = it,
                    contentDescription = null, // Decorative, text provides context
                    modifier = Modifier.size(18.dp)
                )
            }
            
            Text(
                text = text,
                style = AccessibilityUtils.getAccessibleTextStyle(
                    MaterialTheme.typography.labelMedium,
                    accessibilitySettings
                ),
                fontWeight = FontWeight.Medium
            )
        }
    }
}

/**
 * Accessible icon button with proper focus indicators
 */
@Composable
fun AccessibleIconButton(
    icon: ImageVector,
    contentDescription: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
    tint: Color = MaterialTheme.colorScheme.onSurface
) {
    val accessibilitySettings = rememberAccessibilitySettings()
    val minTouchTarget = AccessibilityUtils.getMinimumTouchTargetSize(accessibilitySettings)
    var isFocused by remember { mutableStateOf(false) }
    
    Box(
        modifier = modifier
            .size(minTouchTarget)
            .clip(CircleShape)
            .background(
                if (isFocused && accessibilitySettings.isFocusIndicatorEnabled) {
                    AccessibilityColors.getFocusIndicatorColor().copy(alpha = 0.1f)
                } else Color.Transparent
            )
            .border(
                width = if (isFocused && accessibilitySettings.isFocusIndicatorEnabled) 2.dp else 0.dp,
                color = AccessibilityColors.getFocusIndicatorColor(),
                shape = CircleShape
            )
            .clickable(
                interactionSource = remember { MutableInteractionSource() },
                indication = rememberRipple(bounded = false, radius = minTouchTarget / 2),
                enabled = enabled
            ) { onClick() }
            .focusable()
            .onFocusChanged { isFocused = it.isFocused }
            .semantics {
                this.contentDescription = contentDescription
                role = Role.Button
                if (!enabled) {
                    disabled()
                }
            },
        contentAlignment = Alignment.Center
    ) {
        Icon(
            imageVector = icon,
            contentDescription = null, // Content description is on the container
            tint = AccessibilityUtils.getAccessibleColor(tint, settings = accessibilitySettings),
            modifier = Modifier.size(24.dp)
        )
    }
}

/**
 * Accessible selectable item (for symptoms, moods, etc.)
 */
@Composable
fun AccessibleSelectableItem(
    text: String,
    isSelected: Boolean,
    onSelectionChanged: (Boolean) -> Unit,
    modifier: Modifier = Modifier,
    icon: String? = null,
    description: String? = null,
    enabled: Boolean = true
) {
    val accessibilitySettings = rememberAccessibilitySettings()
    val minTouchTarget = AccessibilityUtils.getMinimumTouchTargetSize(accessibilitySettings)
    var isFocused by remember { mutableStateOf(false) }
    
    val animatedScale by animateFloatAsState(
        targetValue = if (isSelected) 1.05f else 1f,
        animationSpec = tween(
            durationMillis = AccessibilityUtils.getAnimationDuration(200, accessibilitySettings)
        ),
        label = "scale"
    )
    
    Card(
        modifier = modifier
            .defaultMinSize(minWidth = minTouchTarget, minHeight = minTouchTarget)
            .scale(animatedScale)
            .border(
                width = if (isFocused && accessibilitySettings.isFocusIndicatorEnabled) 2.dp else 0.dp,
                color = AccessibilityColors.getFocusIndicatorColor(),
                shape = RoundedCornerShape(12.dp)
            )
            .selectable(
                selected = isSelected,
                enabled = enabled,
                interactionSource = remember { MutableInteractionSource() },
                indication = rememberRipple()
            ) { onSelectionChanged(!isSelected) }
            .focusable()
            .onFocusChanged { isFocused = it.isFocused }
            .semantics {
                val fullDescription = buildString {
                    append(text)
                    description?.let { append(". $it") }
                    append(". ${if (isSelected) "Selected" else "Not selected"}")
                }
                contentDescription = fullDescription
                role = Role.Checkbox
                selected = isSelected
                if (!enabled) {
                    disabled()
                }
            },
        colors = CardDefaults.cardColors(
            containerColor = if (isSelected) {
                AccessibilityUtils.getAccessibleColor(
                    RosePink40.copy(alpha = 0.1f),
                    settings = accessibilitySettings
                )
            } else MaterialTheme.colorScheme.surface
        ),
        elevation = CardDefaults.cardElevation(
            defaultElevation = if (isSelected) 4.dp else 1.dp
        ),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(12.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            icon?.let {
                Text(
                    text = it,
                    style = MaterialTheme.typography.headlineSmall,
                    modifier = Modifier.padding(bottom = 4.dp)
                )
            }
            
            Text(
                text = text,
                style = AccessibilityUtils.getAccessibleTextStyle(
                    MaterialTheme.typography.labelMedium,
                    accessibilitySettings
                ),
                color = if (isSelected) {
                    AccessibilityUtils.getAccessibleColor(RosePink40, settings = accessibilitySettings)
                } else MaterialTheme.colorScheme.onSurface,
                fontWeight = if (isSelected) FontWeight.SemiBold else FontWeight.Normal,
                textAlign = TextAlign.Center
            )
        }
    }
}

/**
 * Accessible pain level selector with detailed descriptions
 */
@Composable
fun AccessiblePainLevelSelector(
    selectedPainLevel: PainLevel,
    onPainLevelChanged: (PainLevel) -> Unit,
    modifier: Modifier = Modifier
) {
    val accessibilitySettings = rememberAccessibilitySettings()
    
    Card(
        modifier = modifier,
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "Pain Level",
                style = AccessibilityUtils.getAccessibleTextStyle(
                    MaterialTheme.typography.titleMedium,
                    accessibilitySettings
                ),
                color = MaterialTheme.colorScheme.onSurface,
                fontWeight = FontWeight.SemiBold,
                modifier = Modifier
                    .padding(bottom = 12.dp)
                    .semantics { heading() }
            )
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                PainLevel.values().forEach { painLevel ->
                    AccessiblePainLevelIndicator(
                        painLevel = painLevel,
                        isSelected = selectedPainLevel == painLevel,
                        onClick = { onPainLevelChanged(painLevel) },
                        modifier = Modifier.weight(1f)
                    )
                }
            }
            
            // Live region for pain level description
            if (selectedPainLevel != PainLevel.NONE) {
                Spacer(modifier = Modifier.height(12.dp))
                Text(
                    text = SemanticDescriptions.getPainLevelDescription(selectedPainLevel),
                    style = AccessibilityUtils.getAccessibleTextStyle(
                        MaterialTheme.typography.bodySmall,
                        accessibilitySettings
                    ),
                    color = DarkGray.copy(alpha = 0.7f),
                    textAlign = TextAlign.Center,
                    modifier = Modifier
                        .fillMaxWidth()
                        .semantics {
                            liveRegion = LiveRegionMode.Polite
                        }
                )
            }
        }
    }
}

@Composable
private fun AccessiblePainLevelIndicator(
    painLevel: PainLevel,
    isSelected: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    val accessibilitySettings = rememberAccessibilitySettings()
    val minTouchTarget = AccessibilityUtils.getMinimumTouchTargetSize(accessibilitySettings)
    var isFocused by remember { mutableStateOf(false) }
    
    val scale by animateFloatAsState(
        targetValue = if (isSelected) 1.2f else 1f,
        animationSpec = tween(
            durationMillis = AccessibilityUtils.getAnimationDuration(200, accessibilitySettings)
        ),
        label = "scale"
    )
    
    Column(
        modifier = modifier.padding(horizontal = 4.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Box(
            modifier = Modifier
                .size(minTouchTarget)
                .scale(scale)
                .clip(CircleShape)
                .background(
                    if (isSelected) {
                        AccessibilityUtils.getAccessibleColor(
                            PeriodPalThemeColors.getPainColor(painLevel),
                            settings = accessibilitySettings
                        )
                    } else LightGray.copy(alpha = 0.3f)
                )
                .border(
                    width = if (isFocused && accessibilitySettings.isFocusIndicatorEnabled) 3.dp else 0.dp,
                    color = AccessibilityColors.getFocusIndicatorColor(),
                    shape = CircleShape
                )
                .clickable(
                    interactionSource = remember { MutableInteractionSource() },
                    indication = rememberRipple(bounded = false)
                ) { onClick() }
                .focusable()
                .onFocusChanged { isFocused = it.isFocused }
                .semantics {
                    contentDescription = SemanticDescriptions.getPainLevelDescription(painLevel)
                    role = Role.RadioButton
                    selected = isSelected
                },
            contentAlignment = Alignment.Center
        ) {
            Text(
                text = painLevel.level.toString(),
                style = AccessibilityUtils.getAccessibleTextStyle(
                    MaterialTheme.typography.labelMedium,
                    accessibilitySettings
                ),
                color = if (isSelected) White else DarkGray,
                fontWeight = FontWeight.Bold
            )
        }
        
        Spacer(modifier = Modifier.height(4.dp))
        
        Text(
            text = painLevel.displayName,
            style = AccessibilityUtils.getAccessibleTextStyle(
                MaterialTheme.typography.labelSmall,
                accessibilitySettings
            ),
            color = if (isSelected) {
                AccessibilityUtils.getAccessibleColor(
                    PeriodPalThemeColors.getPainColor(painLevel),
                    settings = accessibilitySettings
                )
            } else DarkGray,
            fontWeight = if (isSelected) FontWeight.SemiBold else FontWeight.Normal,
            textAlign = TextAlign.Center
        )
    }
}

/**
 * Accessible navigation announcement for screen readers
 */
@Composable
fun AccessibleNavigationAnnouncement(
    currentScreen: String,
    modifier: Modifier = Modifier
) {
    // This component provides live region announcements for navigation changes
    Text(
        text = "Navigated to $currentScreen",
        modifier = modifier.semantics {
            liveRegion = LiveRegionMode.Polite
            contentDescription = "Current screen: $currentScreen"
        },
        style = MaterialTheme.typography.bodySmall,
        color = Color.Transparent // Invisible but accessible
    )
}

@Preview(showBackground = true, name = "Accessible Components Preview")
@Composable
fun AccessibleComponentsPreview() {
    PeriodPalTheme {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            AccessibleButton(
                text = "Log Symptoms",
                onClick = { },
                icon = androidx.compose.material.icons.Icons.Default.Add
            )
            
            Row(
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                AccessibleSelectableItem(
                    text = "Cramps",
                    isSelected = true,
                    onSelectionChanged = { },
                    icon = "🤕",
                    description = "Abdominal cramping pain"
                )
                
                AccessibleSelectableItem(
                    text = "Headache",
                    isSelected = false,
                    onSelectionChanged = { },
                    icon = "🤯",
                    description = "Head pain or pressure"
                )
            }
            
            AccessiblePainLevelSelector(
                selectedPainLevel = PainLevel.MODERATE,
                onPainLevelChanged = { }
            )
        }
    }
}
